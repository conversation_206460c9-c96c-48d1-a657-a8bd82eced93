// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk

/* eslint-disable */
/* eslint-disable eslint-comments/no-restricted-disable */
/* tslint:disable */

// .basehub/runtime/_error.ts
var GenqlError = class extends Error {
  errors = [];
  /**
   * Partial data returned by the server
   */
  data;
  constructor(errors, data) {
    let message = Array.isArray(errors) ? errors.map((x) => x?.message || "").join("\n") : "";
    if (!message) {
      message = "GraphQL error";
    }
    super(message);
    this.errors = errors;
    this.data = data;
  }
};

// .basehub/runtime/_batcher.ts
function dispatchQueueBatch(client, queue) {
  let batchedQuery = queue.map((item) => item.request);
  if (batchedQuery.length === 1) {
    batchedQuery = batchedQuery[0];
  }
  ;
  (() => {
    try {
      return client.fetcher(batchedQuery);
    } catch (e) {
      return Promise.reject(e);
    }
  })().then((responses) => {
    if (queue.length === 1 && !Array.isArray(responses)) {
      if (responses.errors && responses.errors.length) {
        queue[0].reject(
          new GenqlError(responses.errors, responses.data)
        );
        return;
      }
      queue[0].resolve(responses);
      return;
    } else if (responses.length !== queue.length) {
      throw new Error("response length did not match query length");
    }
    for (let i = 0; i < queue.length; i++) {
      if (responses[i].errors && responses[i].errors.length) {
        queue[i].reject(
          new GenqlError(responses[i].errors, responses[i].data)
        );
      } else {
        queue[i].resolve(responses[i]);
      }
    }
  }).catch((e) => {
    for (let i = 0; i < queue.length; i++) {
      queue[i].reject(e);
    }
  });
}
function dispatchQueue(client, options) {
  const queue = client._queue;
  const maxBatchSize = options.maxBatchSize || 0;
  client._queue = [];
  if (maxBatchSize > 0 && maxBatchSize < queue.length) {
    for (let i = 0; i < queue.length / maxBatchSize; i++) {
      dispatchQueueBatch(
        client,
        queue.slice(i * maxBatchSize, (i + 1) * maxBatchSize)
      );
    }
  } else {
    dispatchQueueBatch(client, queue);
  }
}
var QueryBatcher = class _QueryBatcher {
  fetcher;
  _options;
  _queue;
  constructor(fetcher, {
    batchInterval = 16,
    shouldBatch = true,
    maxBatchSize = 0
  } = {}) {
    this.fetcher = fetcher;
    this._options = {
      batchInterval,
      shouldBatch,
      maxBatchSize
    };
    this._queue = [];
  }
  /**
   * Fetch will send a graphql request and return the parsed json.
   * @param {string}      query          - the graphql query.
   * @param {Variables}   variables      - any variables you wish to inject as key/value pairs.
   * @param {[string]}    operationName  - the graphql operationName.
   * @param {Options}     overrides      - the client options overrides.
   *
   * @return {promise} resolves to parsed json of server response
   *
   * @example
   * client.fetch(`
   *    query getHuman($id: ID!) {
   *      human(id: $id) {
   *        name
   *        height
   *      }
   *    }
   * `, { id: "1001" }, 'getHuman')
   *    .then(human => {
   *      // do something with human
   *      console.log(human);
   *    });
   */
  fetch(query, variables, operationName, overrides = {}) {
    const request = {
      query
    };
    const options = Object.assign({}, this._options, overrides);
    if (variables) {
      request.variables = variables;
    }
    if (operationName) {
      request.operationName = operationName;
    }
    const promise = new Promise((resolve, reject) => {
      this._queue.push({
        request,
        resolve,
        reject
      });
      if (this._queue.length === 1) {
        if (options.shouldBatch) {
          setTimeout(
            () => dispatchQueue(this, options),
            options.batchInterval
          );
        } else {
          dispatchQueue(this, options);
        }
      }
    });
    return promise;
  }
  /**
   * Fetch will send a graphql request and return the parsed json.
   * @param {string}      query          - the graphql query.
   * @param {Variables}   variables      - any variables you wish to inject as key/value pairs.
   * @param {[string]}    operationName  - the graphql operationName.
   * @param {Options}     overrides      - the client options overrides.
   *
   * @return {Promise<Array<Result>>} resolves to parsed json of server response
   *
   * @example
   * client.forceFetch(`
   *    query getHuman($id: ID!) {
   *      human(id: $id) {
   *        name
   *        height
   *      }
   *    }
   * `, { id: "1001" }, 'getHuman')
   *    .then(human => {
   *      // do something with human
   *      console.log(human);
   *    });
   */
  forceFetch(query, variables, operationName, overrides = {}) {
    const request = {
      query
    };
    const options = Object.assign({}, this._options, overrides, {
      shouldBatch: false
    });
    if (variables) {
      request.variables = variables;
    }
    if (operationName) {
      request.operationName = operationName;
    }
    const promise = new Promise((resolve, reject) => {
      const client = new _QueryBatcher(this.fetcher, this._options);
      client._queue = [
        {
          request,
          resolve,
          reject
        }
      ];
      dispatchQueue(client, options);
    });
    return promise;
  }
};

// .basehub/runtime/_fetcher.ts
var DEFAULT_BATCH_OPTIONS = {
  maxBatchSize: 10,
  batchInterval: 40
};
var createFetcher = ({
  url,
  headers = {},
  fetcher,
  fetch: _fetch,
  batch = false,
  ...rest
}) => {
  if (!url && !fetcher) {
    throw new Error("url or fetcher is required");
  }
  fetcher = fetcher || (async (body, extraFetchOptions) => {
    let headersObject = typeof headers == "function" ? await headers() : headers;
    headersObject = headersObject || {};
    if (typeof fetch === "undefined" && !_fetch) {
      throw new Error(
        "Global `fetch` function is not available, pass a fetch polyfill to Genql `createClient`"
      );
    }
    let fetchImpl = _fetch || fetch;
    if (extraFetchOptions?.headers) {
      headersObject = {
        ...headersObject,
        ...extraFetchOptions.headers
      };
      delete extraFetchOptions.headers;
    }
    const res = await fetchImpl(url, {
      headers: {
        "Content-Type": "application/json",
        ...headersObject
      },
      method: "POST",
      body: JSON.stringify(body),
      ...rest,
      ...extraFetchOptions
    });
    if (!res.ok) {
      throw new Error(`${res.statusText}: ${await res.text()}`);
    }
    const json = await res.json();
    return json;
  });
  if (!batch) {
    return async (body, extraFetchOptions) => {
      const json = await fetcher(body, extraFetchOptions);
      if (Array.isArray(json)) {
        return json.map((json2) => {
          if (json2?.errors?.length) {
            throw new GenqlError(json2.errors || [], json2.data);
          }
          return json2.data;
        });
      } else {
        if (json?.errors?.length) {
          throw new GenqlError(json.errors || [], json.data);
        }
        return json.data;
      }
    };
  }
  const batcher = new QueryBatcher(
    async (batchedQuery, extraFetchOptions) => {
      const json = await fetcher(batchedQuery, extraFetchOptions);
      return json;
    },
    batch === true ? DEFAULT_BATCH_OPTIONS : batch
  );
  return async ({ query, variables }) => {
    const json = await batcher.fetch(query, variables);
    if (json?.data) {
      return json.data;
    }
    throw new Error(
      "Genql batch fetcher returned unexpected result " + JSON.stringify(json)
    );
  };
};

// .basehub/runtime/_aliasing.js
var aliasSeparator = "__alias__";
function replaceSystemAliases(obj) {
  if (typeof obj !== "object" || obj === null) {
    return obj;
  }
  if (Array.isArray(obj)) {
    return obj.map((item) => replaceSystemAliases(item));
  }
  const newObj = {};
  for (const [key, value] of Object.entries(obj)) {
    if (key.includes(aliasSeparator)) {
      const [_prefix, ...rest] = key.split(aliasSeparator);
      const newKey = rest.join(aliasSeparator);
      newObj[newKey] = replaceSystemAliases(value);
    } else {
      newObj[key] = replaceSystemAliases(value);
    }
  }
  return newObj;
}

// .basehub/runtime/_generate-graphql-operation.ts
var parseRequest = (request, ctx, path, options) => {
  if (typeof request === "object" && "__args" in request) {
    const args = request.__args;
    let fields = { ...request };
    delete fields.__args;
    const argNames = Object.keys(args);
    if (argNames.length === 0) {
      return parseRequest(fields, ctx, path, options);
    }
    const field = getFieldFromPath(ctx.root, path);
    const argStrings = argNames.map((argName) => {
      ctx.varCounter++;
      const varName = `v${ctx.varCounter}`;
      const typing = field.args && field.args[argName];
      if (!typing) {
        throw new Error(
          `no typing defined for argument \`${argName}\` in path \`${path.join(
            "."
          )}\``
        );
      }
      const shouldStringifyValue = ["String", "String!"].includes(
        typing[1]
      );
      let value = args[argName];
      if (shouldStringifyValue) {
        if (typeof value === "object") {
          value = JSON.stringify(value);
        }
      }
      ctx.variables[varName] = {
        value,
        typing
      };
      return `${argName}:$${varName}`;
    });
    return `(${argStrings})${parseRequest(fields, ctx, path, options)}`;
  } else if (typeof request === "object" && Object.keys(request).length > 0) {
    const fields = request;
    const fieldNames = Object.keys(fields).filter((k) => Boolean(fields[k]));
    const type = path.length > 0 ? getFieldFromPath(ctx.root, path).type : ctx.root;
    const scalarFields = type.scalar;
    let scalarFieldsFragment;
    const validFieldNames = fieldNames.filter((f) => {
      if (["__scalar", "__name", "__fragmentOn"].includes(f))
        return true;
      if (f.startsWith("on_"))
        return true;
      return type.fields && f in type.fields;
    });
    if (validFieldNames.length === 0) {
      return "";
    }
    if (fieldNames.includes("__scalar")) {
      const falsyFieldNames = new Set(
        Object.keys(fields).filter((k) => !Boolean(fields[k]))
      );
      if (scalarFields?.length) {
        ctx.fragmentCounter++;
        scalarFieldsFragment = `f${ctx.fragmentCounter}`;
        ctx.fragments.push(
          `fragment ${scalarFieldsFragment} on ${type.name}{${scalarFields.filter((f) => !falsyFieldNames.has(f)).map(
            (f) => `${options?.aliasPrefix ? `${options.aliasPrefix}${aliasSeparator}${f}: ` : ""}${f}`
          ).join(",")}}`
        );
      }
    }
    const fieldsSelection = validFieldNames.filter((f) => !["__scalar", "__name", "__fragmentOn"].includes(f)).map((f) => {
      if (f.startsWith("on_")) {
        ctx.fragmentCounter++;
        const implementationFragment = `f${ctx.fragmentCounter}`;
        const parsed = parseRequest(fields[f], ctx, [...path, f], {
          ...options,
          aliasPrefix: implementationFragment
        });
        const typeMatch = f.match(/^on_(.+)/);
        if (!typeMatch || !typeMatch[1])
          throw new Error("match failed");
        ctx.fragments.push(
          `fragment ${implementationFragment} on ${typeMatch[1]}${parsed}`
        );
        return `...${implementationFragment}`;
      } else {
        const field = type.fields?.[f];
        if (!field)
          return "";
        if (!field.type.fields) {
          return `${options?.aliasPrefix ? `${options.aliasPrefix}${aliasSeparator}${f}: ` : ""}${f}`;
        }
        const parsed = parseRequest(
          fields[f],
          ctx,
          [...path, f],
          options
        );
        if (!parsed && field.type.fields) {
          const firstScalar = field.type.scalar?.[0];
          if (firstScalar) {
            return `${options?.aliasPrefix ? `${options.aliasPrefix}${aliasSeparator}${f}: ` : ""}${f}{${firstScalar}}`;
          }
        }
        return `${options?.aliasPrefix ? `${options.aliasPrefix}${aliasSeparator}${f}: ` : ""}${f}${parsed}`;
      }
    }).filter(Boolean).concat(scalarFieldsFragment ? [`...${scalarFieldsFragment}`] : []).join(",");
    return fieldsSelection ? `{${fieldsSelection}}` : "";
  } else {
    return "";
  }
};
var generateGraphqlOperation = (operation, root, fields) => {
  const ctx = {
    root,
    varCounter: 0,
    variables: {},
    fragmentCounter: 0,
    fragments: []
  };
  const result = parseRequest(fields, ctx, []);
  const varNames = Object.keys(ctx.variables);
  const varsString = varNames.length > 0 ? `(${varNames.map((v) => {
    const variableType = ctx.variables[v].typing[1];
    return `$${v}:${variableType}`;
  })})` : "";
  const operationName = fields?.__name || "";
  return {
    query: [
      `${operation} ${operationName}${varsString}${result}`,
      ...ctx.fragments
    ].join(","),
    variables: Object.keys(ctx.variables).reduce(
      (r, v) => {
        r[v] = ctx.variables[v].value;
        return r;
      },
      {}
    ),
    ...operationName ? { operationName: operationName.toString() } : {}
  };
};
var getFieldFromPath = (root, path) => {
  let current;
  if (!root)
    throw new Error("root type is not provided");
  if (path.length === 0)
    throw new Error(`path is empty`);
  path.forEach((f) => {
    const type = current ? current.type : root;
    if (!type.fields)
      throw new Error(`type \`${type.name}\` does not have fields`);
    const possibleTypes = Object.keys(type.fields).filter((i) => i.startsWith("on_")).reduce(
      (types, fieldName) => {
        const field2 = type.fields && type.fields[fieldName];
        if (field2)
          types.push(field2.type);
        return types;
      },
      [type]
    );
    let field = null;
    possibleTypes.forEach((type2) => {
      const found = type2.fields && type2.fields[f];
      if (found)
        field = found;
    });
    if (!field)
      throw new Error(
        `type \`${type.name}\` does not have a field \`${f}\``
      );
    current = field;
  });
  return current;
};

// .basehub/runtime/_create-client.ts
var createClient = ({
  queryRoot,
  mutationRoot,
  subscriptionRoot,
  getExtraFetchOptions,
  ...options
}) => {
  const fetcher = createFetcher(options);
  const client = {};
  if (queryRoot) {
    client.query = async (request) => {
      if (!queryRoot)
        throw new Error("queryRoot argument is missing");
      const body = generateGraphqlOperation("query", queryRoot, request);
      const extraFetchOptions = await getExtraFetchOptions?.(
        "query",
        body,
        request
      );
      return await fetcher(body, extraFetchOptions).then(
        (result) => replaceSystemAliases(result)
      );
    };
  }
  if (mutationRoot) {
    client.mutation = async (request) => {
      if (!mutationRoot)
        throw new Error("mutationRoot argument is missing");
      const body = generateGraphqlOperation(
        "mutation",
        mutationRoot,
        request
      );
      const extraFetchOptions = await getExtraFetchOptions?.(
        "mutation",
        body,
        request
      );
      return await fetcher(
        generateGraphqlOperation("mutation", mutationRoot, request),
        extraFetchOptions
      );
    };
  }
  return client;
};
createClient.replaceSystemAliases = replaceSystemAliases;

// .basehub/runtime/_link-type-map.ts
var linkTypeMap = (typeMap2) => {
  const indexToName = Object.assign(
    {},
    ...Object.keys(typeMap2.types).map((k, i) => ({ [i]: k }))
  );
  let intermediaryTypeMap = Object.assign(
    {},
    ...Object.keys(typeMap2.types || {}).map(
      (k) => {
        const type = typeMap2.types[k];
        const fields = type || {};
        return {
          [k]: {
            name: k,
            // type scalar properties
            scalar: Object.keys(fields).filter((f) => {
              const [type2] = fields[f] || [];
              const isScalar = type2 && typeMap2.scalars.includes(type2);
              if (!isScalar) {
                return false;
              }
              const args = fields[f]?.[1];
              const argTypes = Object.values(args || {}).map((x) => x?.[1]).filter(Boolean);
              const hasRequiredArgs = argTypes.some(
                (str) => str && str.endsWith("!")
              );
              if (hasRequiredArgs) {
                return false;
              }
              return true;
            }),
            // fields with corresponding `type` and `args`
            fields: Object.assign(
              {},
              ...Object.keys(fields).map(
                (f) => {
                  const [typeIndex, args] = fields[f] || [];
                  if (typeIndex == null) {
                    return {};
                  }
                  return {
                    [f]: {
                      // replace index with type name
                      type: indexToName[typeIndex],
                      args: Object.assign(
                        {},
                        ...Object.keys(args || {}).map(
                          (k2) => {
                            if (!args || !args[k2]) {
                              return;
                            }
                            const [
                              argTypeName,
                              argTypeString
                            ] = args[k2];
                            return {
                              [k2]: [
                                indexToName[argTypeName],
                                argTypeString || indexToName[argTypeName]
                              ]
                            };
                          }
                        )
                      )
                    }
                  };
                }
              )
            )
          }
        };
      }
    )
  );
  const res = resolveConcreteTypes(intermediaryTypeMap);
  return res;
};
var resolveConcreteTypes = (linkedTypeMap) => {
  Object.keys(linkedTypeMap).forEach((typeNameFromKey) => {
    const type = linkedTypeMap[typeNameFromKey];
    if (!type.fields) {
      return;
    }
    const fields = type.fields;
    Object.keys(fields).forEach((f) => {
      const field = fields[f];
      if (field.args) {
        const args = field.args;
        Object.keys(args).forEach((key) => {
          const arg = args[key];
          if (arg) {
            const [typeName2] = arg;
            if (typeof typeName2 === "string") {
              if (!linkedTypeMap[typeName2]) {
                linkedTypeMap[typeName2] = { name: typeName2 };
              }
              arg[0] = linkedTypeMap[typeName2];
            }
          }
        });
      }
      const typeName = field.type;
      if (typeof typeName === "string") {
        if (!linkedTypeMap[typeName]) {
          linkedTypeMap[typeName] = { name: typeName };
        }
        field.type = linkedTypeMap[typeName];
      }
    });
  });
  return linkedTypeMap;
};

// .basehub/types.ts
var types_default = {
  "scalars": [
    0,
    4,
    5,
    6,
    7,
    25,
    29,
    30,
    32,
    33,
    35,
    36,
    37,
    41,
    53,
    58,
    63,
    71,
    72
  ],
  "types": {
    "AnalyticsKeyScope": {},
    "Authors": {
      "_analyticsKey": [
        58,
        {
          "scope": [
            0
          ]
        }
      ],
      "_dashboardUrl": [
        58
      ],
      "_id": [
        58
      ],
      "_idPath": [
        58
      ],
      "_meta": [
        43
      ],
      "_searchKey": [
        58
      ],
      "_slug": [
        58
      ],
      "_slugPath": [
        58
      ],
      "_sys": [
        13
      ],
      "_title": [
        58
      ],
      "item": [
        2
      ],
      "items": [
        2
      ],
      "__typename": [
        58
      ]
    },
    "AuthorsItem": {
      "_analyticsKey": [
        58,
        {
          "scope": [
            0
          ]
        }
      ],
      "_dashboardUrl": [
        58
      ],
      "_id": [
        58
      ],
      "_idPath": [
        58
      ],
      "_slug": [
        58
      ],
      "_slugPath": [
        58
      ],
      "_sys": [
        13
      ],
      "_title": [
        58
      ],
      "avatar": [
        15
      ],
      "xUrl": [
        58
      ],
      "__typename": [
        58
      ]
    },
    "AuthorsItemFilterInput": {
      "AND": [
        3
      ],
      "OR": [
        3
      ],
      "_id": [
        59
      ],
      "_slug": [
        59
      ],
      "_sys_apiNamePath": [
        59
      ],
      "_sys_createdAt": [
        31
      ],
      "_sys_hash": [
        59
      ],
      "_sys_id": [
        59
      ],
      "_sys_idPath": [
        59
      ],
      "_sys_lastModifiedAt": [
        31
      ],
      "_sys_slug": [
        59
      ],
      "_sys_slugPath": [
        59
      ],
      "_sys_title": [
        59
      ],
      "_title": [
        59
      ],
      "xUrl": [
        59
      ],
      "__typename": [
        58
      ]
    },
    "AuthorsItemOrderByEnum": {},
    "BSHBEventSchema": {},
    "BSHBRichTextContentSchema": {},
    "BSHBRichTextTOCSchema": {},
    "BaseRichTextJson": {
      "blocks": [
        58
      ],
      "content": [
        6
      ],
      "toc": [
        7
      ],
      "__typename": [
        58
      ]
    },
    "BlockAudio": {
      "duration": [
        33
      ],
      "fileName": [
        58
      ],
      "fileSize": [
        36
      ],
      "lastModified": [
        33
      ],
      "mimeType": [
        58
      ],
      "url": [
        58
      ],
      "__typename": [
        58
      ]
    },
    "BlockCodeSnippet": {
      "allowedLanguages": [
        30
      ],
      "code": [
        58
      ],
      "html": [
        58,
        {
          "theme": [
            58
          ]
        }
      ],
      "language": [
        30
      ],
      "__typename": [
        58
      ]
    },
    "BlockColor": {
      "b": [
        36
      ],
      "g": [
        36
      ],
      "hex": [
        58
      ],
      "hsl": [
        58
      ],
      "r": [
        36
      ],
      "rgb": [
        58
      ],
      "__typename": [
        58
      ]
    },
    "BlockDocument": {
      "_analyticsKey": [
        58,
        {
          "scope": [
            0
          ]
        }
      ],
      "_dashboardUrl": [
        58
      ],
      "_id": [
        58
      ],
      "_idPath": [
        58
      ],
      "_slug": [
        58
      ],
      "_slugPath": [
        58
      ],
      "_sys": [
        13
      ],
      "_title": [
        58
      ],
      "on_Authors": [
        1
      ],
      "on_AuthorsItem": [
        2
      ],
      "on_Blog": [
        20
      ],
      "on_Categories": [
        26
      ],
      "on_CategoriesItem": [
        27
      ],
      "on_LegalPages": [
        38
      ],
      "on_LegalPagesItem": [
        39
      ],
      "on_Posts": [
        48
      ],
      "on_PostsItem": [
        49
      ],
      "on__AgentSTART": [
        65
      ],
      "on_authorsItem_AsList": [
        75
      ],
      "on_categoriesItem_AsList": [
        76
      ],
      "on_legalPagesItem_AsList": [
        77
      ],
      "on_postsItem_AsList": [
        78
      ],
      "__typename": [
        58
      ]
    },
    "BlockDocumentSys": {
      "apiNamePath": [
        58
      ],
      "createdAt": [
        58
      ],
      "hash": [
        58
      ],
      "id": [
        35
      ],
      "idPath": [
        58
      ],
      "lastModifiedAt": [
        58
      ],
      "slug": [
        58
      ],
      "slugPath": [
        58
      ],
      "title": [
        58
      ],
      "__typename": [
        58
      ]
    },
    "BlockFile": {
      "fileName": [
        58
      ],
      "fileSize": [
        36
      ],
      "lastModified": [
        33
      ],
      "mimeType": [
        58
      ],
      "url": [
        58
      ],
      "__typename": [
        58
      ]
    },
    "BlockImage": {
      "alt": [
        58
      ],
      "aspectRatio": [
        58
      ],
      "blurDataURL": [
        58
      ],
      "fileName": [
        58
      ],
      "fileSize": [
        36
      ],
      "height": [
        36
      ],
      "lastModified": [
        33
      ],
      "mimeType": [
        58
      ],
      "placeholderURL": [
        58
      ],
      "rawUrl": [
        58
      ],
      "thumbhash": [
        58
      ],
      "url": [
        58,
        {
          "anim": [
            58
          ],
          "background": [
            58
          ],
          "blur": [
            36
          ],
          "border": [
            58
          ],
          "brightness": [
            36
          ],
          "compression": [
            58
          ],
          "contrast": [
            36
          ],
          "dpr": [
            36
          ],
          "fit": [
            58
          ],
          "format": [
            58
          ],
          "gamma": [
            58
          ],
          "gravity": [
            58
          ],
          "height": [
            36
          ],
          "metadata": [
            58
          ],
          "quality": [
            36
          ],
          "rotate": [
            58
          ],
          "sharpen": [
            58
          ],
          "trim": [
            58
          ],
          "width": [
            36
          ]
        }
      ],
      "width": [
        36
      ],
      "__typename": [
        58
      ]
    },
    "BlockList": {
      "_analyticsKey": [
        58,
        {
          "scope": [
            0
          ]
        }
      ],
      "_dashboardUrl": [
        58
      ],
      "_id": [
        58
      ],
      "_idPath": [
        58
      ],
      "_meta": [
        43
      ],
      "_searchKey": [
        58
      ],
      "_slug": [
        58
      ],
      "_slugPath": [
        58
      ],
      "_sys": [
        13
      ],
      "_title": [
        58
      ],
      "on_Authors": [
        1
      ],
      "on_Categories": [
        26
      ],
      "on_LegalPages": [
        38
      ],
      "on_Posts": [
        48
      ],
      "on_authorsItem_AsList": [
        75
      ],
      "on_categoriesItem_AsList": [
        76
      ],
      "on_legalPagesItem_AsList": [
        77
      ],
      "on_postsItem_AsList": [
        78
      ],
      "__typename": [
        58
      ]
    },
    "BlockOgImage": {
      "height": [
        36
      ],
      "url": [
        58
      ],
      "width": [
        36
      ],
      "__typename": [
        58
      ]
    },
    "BlockRichText": {
      "html": [
        58,
        {
          "slugs": [
            25
          ],
          "toc": [
            25
          ]
        }
      ],
      "json": [
        56
      ],
      "markdown": [
        58
      ],
      "plainText": [
        58
      ],
      "readingTime": [
        36,
        {
          "wpm": [
            36
          ]
        }
      ],
      "on_Body": [
        21
      ],
      "on_Body_1": [
        23
      ],
      "__typename": [
        58
      ]
    },
    "BlockVideo": {
      "aspectRatio": [
        58
      ],
      "duration": [
        33
      ],
      "fileName": [
        58
      ],
      "fileSize": [
        36
      ],
      "height": [
        36
      ],
      "lastModified": [
        33
      ],
      "mimeType": [
        58
      ],
      "url": [
        58
      ],
      "width": [
        36
      ],
      "__typename": [
        58
      ]
    },
    "Blog": {
      "_analyticsKey": [
        58,
        {
          "scope": [
            0
          ]
        }
      ],
      "_dashboardUrl": [
        58
      ],
      "_id": [
        58
      ],
      "_idPath": [
        58
      ],
      "_slug": [
        58
      ],
      "_slugPath": [
        58
      ],
      "_sys": [
        13
      ],
      "_title": [
        58
      ],
      "authors": [
        1,
        {
          "filter": [
            3
          ],
          "first": [
            36
          ],
          "orderBy": [
            4
          ],
          "skip": [
            36
          ]
        }
      ],
      "categories": [
        26,
        {
          "filter": [
            28
          ],
          "first": [
            36
          ],
          "orderBy": [
            29
          ],
          "skip": [
            36
          ]
        }
      ],
      "posts": [
        48,
        {
          "filter": [
            50
          ],
          "first": [
            36
          ],
          "orderBy": [
            53
          ],
          "skip": [
            36
          ]
        }
      ],
      "__typename": [
        58
      ]
    },
    "Body": {
      "html": [
        58,
        {
          "slugs": [
            25
          ],
          "toc": [
            25
          ]
        }
      ],
      "json": [
        22
      ],
      "markdown": [
        58
      ],
      "plainText": [
        58
      ],
      "readingTime": [
        36,
        {
          "wpm": [
            36
          ]
        }
      ],
      "__typename": [
        58
      ]
    },
    "BodyRichText": {
      "content": [
        6
      ],
      "toc": [
        7
      ],
      "__typename": [
        58
      ]
    },
    "Body_1": {
      "html": [
        58,
        {
          "slugs": [
            25
          ],
          "toc": [
            25
          ]
        }
      ],
      "json": [
        24
      ],
      "markdown": [
        58
      ],
      "plainText": [
        58
      ],
      "readingTime": [
        36,
        {
          "wpm": [
            36
          ]
        }
      ],
      "__typename": [
        58
      ]
    },
    "Body_1RichText": {
      "content": [
        6
      ],
      "toc": [
        7
      ],
      "__typename": [
        58
      ]
    },
    "Boolean": {},
    "Categories": {
      "_analyticsKey": [
        58,
        {
          "scope": [
            0
          ]
        }
      ],
      "_dashboardUrl": [
        58
      ],
      "_id": [
        58
      ],
      "_idPath": [
        58
      ],
      "_meta": [
        43
      ],
      "_searchKey": [
        58
      ],
      "_slug": [
        58
      ],
      "_slugPath": [
        58
      ],
      "_sys": [
        13
      ],
      "_title": [
        58
      ],
      "item": [
        27
      ],
      "items": [
        27
      ],
      "__typename": [
        58
      ]
    },
    "CategoriesItem": {
      "_analyticsKey": [
        58,
        {
          "scope": [
            0
          ]
        }
      ],
      "_dashboardUrl": [
        58
      ],
      "_id": [
        58
      ],
      "_idPath": [
        58
      ],
      "_slug": [
        58
      ],
      "_slugPath": [
        58
      ],
      "_sys": [
        13
      ],
      "_title": [
        58
      ],
      "__typename": [
        58
      ]
    },
    "CategoriesItemFilterInput": {
      "AND": [
        28
      ],
      "OR": [
        28
      ],
      "_id": [
        59
      ],
      "_slug": [
        59
      ],
      "_sys_apiNamePath": [
        59
      ],
      "_sys_createdAt": [
        31
      ],
      "_sys_hash": [
        59
      ],
      "_sys_id": [
        59
      ],
      "_sys_idPath": [
        59
      ],
      "_sys_lastModifiedAt": [
        31
      ],
      "_sys_slug": [
        59
      ],
      "_sys_slugPath": [
        59
      ],
      "_sys_title": [
        59
      ],
      "_title": [
        59
      ],
      "__typename": [
        58
      ]
    },
    "CategoriesItemOrderByEnum": {},
    "CodeSnippetLanguage": {},
    "DateFilter": {
      "eq": [
        32
      ],
      "isAfter": [
        32
      ],
      "isBefore": [
        32
      ],
      "isNull": [
        25
      ],
      "neq": [
        32
      ],
      "onOrAfter": [
        32
      ],
      "onOrBefore": [
        32
      ],
      "__typename": [
        58
      ]
    },
    "DateTime": {},
    "Float": {},
    "GetUploadSignedURL": {
      "signedURL": [
        58
      ],
      "uploadURL": [
        58
      ],
      "__typename": [
        58
      ]
    },
    "ID": {},
    "Int": {},
    "JSON": {},
    "LegalPages": {
      "_analyticsKey": [
        58,
        {
          "scope": [
            0
          ]
        }
      ],
      "_dashboardUrl": [
        58
      ],
      "_id": [
        58
      ],
      "_idPath": [
        58
      ],
      "_meta": [
        43
      ],
      "_searchKey": [
        58
      ],
      "_slug": [
        58
      ],
      "_slugPath": [
        58
      ],
      "_sys": [
        13
      ],
      "_title": [
        58
      ],
      "item": [
        39
      ],
      "items": [
        39
      ],
      "__typename": [
        58
      ]
    },
    "LegalPagesItem": {
      "_analyticsKey": [
        58,
        {
          "scope": [
            0
          ]
        }
      ],
      "_dashboardUrl": [
        58
      ],
      "_id": [
        58
      ],
      "_idPath": [
        58
      ],
      "_slug": [
        58
      ],
      "_slugPath": [
        58
      ],
      "_sys": [
        13
      ],
      "_title": [
        58
      ],
      "body": [
        23
      ],
      "description": [
        58
      ],
      "__typename": [
        58
      ]
    },
    "LegalPagesItemFilterInput": {
      "AND": [
        40
      ],
      "OR": [
        40
      ],
      "_id": [
        59
      ],
      "_slug": [
        59
      ],
      "_sys_apiNamePath": [
        59
      ],
      "_sys_createdAt": [
        31
      ],
      "_sys_hash": [
        59
      ],
      "_sys_id": [
        59
      ],
      "_sys_idPath": [
        59
      ],
      "_sys_lastModifiedAt": [
        31
      ],
      "_sys_slug": [
        59
      ],
      "_sys_slugPath": [
        59
      ],
      "_sys_title": [
        59
      ],
      "_title": [
        59
      ],
      "description": [
        59
      ],
      "__typename": [
        58
      ]
    },
    "LegalPagesItemOrderByEnum": {},
    "ListFilter": {
      "isEmpty": [
        25
      ],
      "length": [
        36
      ],
      "__typename": [
        58
      ]
    },
    "ListMeta": {
      "filteredCount": [
        36
      ],
      "totalCount": [
        36
      ],
      "__typename": [
        58
      ]
    },
    "MediaBlock": {
      "fileName": [
        58
      ],
      "fileSize": [
        36
      ],
      "lastModified": [
        33
      ],
      "mimeType": [
        58
      ],
      "url": [
        58
      ],
      "on_BlockAudio": [
        9
      ],
      "on_BlockFile": [
        14
      ],
      "on_BlockImage": [
        15
      ],
      "on_BlockVideo": [
        19
      ],
      "__typename": [
        58
      ]
    },
    "MediaBlockUnion": {
      "on_BlockAudio": [
        9
      ],
      "on_BlockFile": [
        14
      ],
      "on_BlockImage": [
        15
      ],
      "on_BlockVideo": [
        19
      ],
      "on_MediaBlock": [
        44
      ],
      "__typename": [
        58
      ]
    },
    "Mutation": {
      "getUploadSignedURL": [
        34,
        {
          "fileHash": [
            58
          ],
          "fileName": [
            58,
            "String!"
          ]
        }
      ],
      "transaction": [
        62,
        {
          "authorId": [
            58
          ],
          "autoCommit": [
            58
          ],
          "data": [
            58,
            "String!"
          ],
          "skipWorkflows": [
            25
          ],
          "timeout": [
            36
          ]
        }
      ],
      "transactionAsync": [
        58,
        {
          "authorId": [
            58
          ],
          "autoCommit": [
            58
          ],
          "data": [
            58,
            "String!"
          ],
          "skipWorkflows": [
            25
          ]
        }
      ],
      "transactionStatus": [
        62,
        {
          "id": [
            58,
            "String!"
          ]
        }
      ],
      "__typename": [
        58
      ]
    },
    "NumberFilter": {
      "eq": [
        33
      ],
      "gt": [
        33
      ],
      "gte": [
        33
      ],
      "isNull": [
        25
      ],
      "lt": [
        33
      ],
      "lte": [
        33
      ],
      "neq": [
        33
      ],
      "__typename": [
        58
      ]
    },
    "Posts": {
      "_analyticsKey": [
        58,
        {
          "scope": [
            0
          ]
        }
      ],
      "_dashboardUrl": [
        58
      ],
      "_id": [
        58
      ],
      "_idPath": [
        58
      ],
      "_meta": [
        43
      ],
      "_searchKey": [
        58
      ],
      "_slug": [
        58
      ],
      "_slugPath": [
        58
      ],
      "_sys": [
        13
      ],
      "_title": [
        58
      ],
      "item": [
        49
      ],
      "items": [
        49
      ],
      "__typename": [
        58
      ]
    },
    "PostsItem": {
      "_analyticsKey": [
        58,
        {
          "scope": [
            0
          ]
        }
      ],
      "_dashboardUrl": [
        58
      ],
      "_id": [
        58
      ],
      "_idPath": [
        58
      ],
      "_slug": [
        58
      ],
      "_slugPath": [
        58
      ],
      "_sys": [
        13
      ],
      "_title": [
        58
      ],
      "authors": [
        2
      ],
      "body": [
        21
      ],
      "categories": [
        27
      ],
      "date": [
        58
      ],
      "description": [
        58
      ],
      "image": [
        15
      ],
      "__typename": [
        58
      ]
    },
    "PostsItemFilterInput": {
      "AND": [
        50
      ],
      "OR": [
        50
      ],
      "_id": [
        59
      ],
      "_slug": [
        59
      ],
      "_sys_apiNamePath": [
        59
      ],
      "_sys_createdAt": [
        31
      ],
      "_sys_hash": [
        59
      ],
      "_sys_id": [
        59
      ],
      "_sys_idPath": [
        59
      ],
      "_sys_lastModifiedAt": [
        31
      ],
      "_sys_slug": [
        59
      ],
      "_sys_slugPath": [
        59
      ],
      "_sys_title": [
        59
      ],
      "_title": [
        59
      ],
      "authors": [
        51
      ],
      "categories": [
        52
      ],
      "date": [
        31
      ],
      "description": [
        59
      ],
      "__typename": [
        58
      ]
    },
    "PostsItemFilterInput__authors_0___untitled": {
      "_id": [
        59
      ],
      "_slug": [
        59
      ],
      "_sys_apiNamePath": [
        59
      ],
      "_sys_createdAt": [
        31
      ],
      "_sys_hash": [
        59
      ],
      "_sys_id": [
        59
      ],
      "_sys_idPath": [
        59
      ],
      "_sys_lastModifiedAt": [
        31
      ],
      "_sys_slug": [
        59
      ],
      "_sys_slugPath": [
        59
      ],
      "_sys_title": [
        59
      ],
      "_title": [
        59
      ],
      "xUrl": [
        59
      ],
      "__typename": [
        58
      ]
    },
    "PostsItemFilterInput__categories_0___untitled": {
      "_id": [
        59
      ],
      "_slug": [
        59
      ],
      "_sys_apiNamePath": [
        59
      ],
      "_sys_createdAt": [
        31
      ],
      "_sys_hash": [
        59
      ],
      "_sys_id": [
        59
      ],
      "_sys_idPath": [
        59
      ],
      "_sys_lastModifiedAt": [
        31
      ],
      "_sys_slug": [
        59
      ],
      "_sys_slugPath": [
        59
      ],
      "_sys_title": [
        59
      ],
      "_title": [
        59
      ],
      "__typename": [
        58
      ]
    },
    "PostsItemOrderByEnum": {},
    "Query": {
      "_agent": [
        65,
        {
          "id": [
            58,
            "String!"
          ]
        }
      ],
      "_agents": [
        73
      ],
      "_componentInstances": [
        74
      ],
      "_structure": [
        37,
        {
          "format": [
            72
          ],
          "resolveTargetsWith": [
            71
          ],
          "targetBlock": [
            61
          ],
          "withConstraints": [
            25
          ],
          "withIDs": [
            25
          ],
          "withTypeOptions": [
            25
          ]
        }
      ],
      "_sys": [
        55
      ],
      "blog": [
        20
      ],
      "legalPages": [
        38,
        {
          "filter": [
            40
          ],
          "first": [
            36
          ],
          "orderBy": [
            41
          ],
          "skip": [
            36
          ]
        }
      ],
      "__typename": [
        58
      ]
    },
    "RepoSys": {
      "branches": [
        67,
        {
          "limit": [
            36
          ],
          "offset": [
            36
          ]
        }
      ],
      "hash": [
        58
      ],
      "id": [
        35
      ],
      "playgroundInfo": [
        70
      ],
      "slug": [
        58
      ],
      "title": [
        58
      ],
      "__typename": [
        58
      ]
    },
    "RichTextJson": {
      "content": [
        6
      ],
      "toc": [
        7
      ],
      "on_BaseRichTextJson": [
        8
      ],
      "on_BodyRichText": [
        22
      ],
      "on_Body_1RichText": [
        24
      ],
      "__typename": [
        58
      ]
    },
    "SelectFilter": {
      "excludes": [
        58
      ],
      "excludesAll": [
        58
      ],
      "includes": [
        58
      ],
      "includesAll": [
        58
      ],
      "includesAny": [
        58
      ],
      "isEmpty": [
        25
      ],
      "__typename": [
        58
      ]
    },
    "String": {},
    "StringFilter": {
      "contains": [
        58
      ],
      "endsWith": [
        58
      ],
      "eq": [
        58
      ],
      "in": [
        58
      ],
      "isNull": [
        25
      ],
      "matches": [
        60
      ],
      "notEq": [
        58
      ],
      "notIn": [
        58
      ],
      "startsWith": [
        58
      ],
      "__typename": [
        58
      ]
    },
    "StringMatchesFilter": {
      "caseSensitive": [
        25
      ],
      "pattern": [
        58
      ],
      "__typename": [
        58
      ]
    },
    "TargetBlock": {
      "focus": [
        25
      ],
      "id": [
        58
      ],
      "label": [
        58
      ],
      "__typename": [
        58
      ]
    },
    "TransactionStatus": {
      "duration": [
        36
      ],
      "endedAt": [
        58
      ],
      "id": [
        58
      ],
      "message": [
        58
      ],
      "startedAt": [
        58
      ],
      "status": [
        63
      ],
      "__typename": [
        58
      ]
    },
    "TransactionStatusEnum": {},
    "Variant": {
      "apiName": [
        58
      ],
      "color": [
        58
      ],
      "id": [
        58
      ],
      "isDefault": [
        25
      ],
      "label": [
        58
      ],
      "__typename": [
        58
      ]
    },
    "_AgentSTART": {
      "_agentKey": [
        58
      ],
      "_analyticsKey": [
        58,
        {
          "scope": [
            0
          ]
        }
      ],
      "_dashboardUrl": [
        58
      ],
      "_id": [
        58
      ],
      "_idPath": [
        58
      ],
      "_slug": [
        58
      ],
      "_slugPath": [
        58
      ],
      "_sys": [
        13
      ],
      "_title": [
        58
      ],
      "accent": [
        58
      ],
      "avatar": [
        58
      ],
      "chatUrl": [
        58
      ],
      "commit": [
        25
      ],
      "description": [
        58
      ],
      "edit": [
        25
      ],
      "embedUrl": [
        58
      ],
      "getUserInfo": [
        25
      ],
      "grayscale": [
        58
      ],
      "manageBranches": [
        25
      ],
      "mcpUrl": [
        58
      ],
      "model": [
        58
      ],
      "searchTheWeb": [
        25
      ],
      "slackInstallUrl": [
        58
      ],
      "systemPrompt": [
        58
      ],
      "__typename": [
        58
      ]
    },
    "_BranchInfo": {
      "archivedAt": [
        58
      ],
      "archivedBy": [
        58
      ],
      "authorId": [
        58
      ],
      "contributors": [
        58
      ],
      "createdAt": [
        58
      ],
      "description": [
        58
      ],
      "git": [
        69
      ],
      "headCommit": [
        68
      ],
      "headCommitId": [
        58
      ],
      "id": [
        35
      ],
      "inlineSuggestionAppliedAt": [
        58
      ],
      "isDefault": [
        25
      ],
      "isInlineSuggestion": [
        25
      ],
      "name": [
        58
      ],
      "playgroundId": [
        58
      ],
      "rollbackCommitId": [
        58
      ],
      "rollbackIsoDate": [
        58
      ],
      "sourceBranchId": [
        58
      ],
      "updatedAt": [
        58
      ],
      "workingRootBlockId": [
        58
      ],
      "__typename": [
        58
      ]
    },
    "_Branches": {
      "_meta": [
        43
      ],
      "items": [
        66
      ],
      "__typename": [
        58
      ]
    },
    "_CommitInfo": {
      "authorId": [
        58
      ],
      "branchId": [
        58
      ],
      "contributors": [
        58
      ],
      "createdAt": [
        58
      ],
      "hash": [
        58
      ],
      "id": [
        58
      ],
      "mergeParentCommitId": [
        58
      ],
      "message": [
        58
      ],
      "parentCommitId": [
        58
      ],
      "playgroundId": [
        58
      ],
      "repoId": [
        58
      ],
      "rootBlockId": [
        58
      ],
      "__typename": [
        58
      ]
    },
    "_GitInfo": {
      "branch": [
        58
      ],
      "deploymentUrl": [
        58
      ],
      "__typename": [
        58
      ]
    },
    "_PlaygroundInfo": {
      "claimUrl": [
        58
      ],
      "editUrl": [
        58
      ],
      "expiresAt": [
        58
      ],
      "id": [
        58
      ],
      "__typename": [
        58
      ]
    },
    "_ResolveTargetsWithEnum": {},
    "_StructureFormatEnum": {},
    "_agents": {
      "start": [
        65
      ],
      "__typename": [
        58
      ]
    },
    "_components": {
      "authorsItem": [
        75,
        {
          "filter": [
            3
          ],
          "first": [
            36
          ],
          "orderBy": [
            4
          ],
          "skip": [
            36
          ]
        }
      ],
      "categoriesItem": [
        76,
        {
          "filter": [
            28
          ],
          "first": [
            36
          ],
          "orderBy": [
            29
          ],
          "skip": [
            36
          ]
        }
      ],
      "legalPagesItem": [
        77,
        {
          "filter": [
            40
          ],
          "first": [
            36
          ],
          "orderBy": [
            41
          ],
          "skip": [
            36
          ]
        }
      ],
      "postsItem": [
        78,
        {
          "filter": [
            50
          ],
          "first": [
            36
          ],
          "orderBy": [
            53
          ],
          "skip": [
            36
          ]
        }
      ],
      "__typename": [
        58
      ]
    },
    "authorsItem_AsList": {
      "_analyticsKey": [
        58,
        {
          "scope": [
            0
          ]
        }
      ],
      "_dashboardUrl": [
        58
      ],
      "_id": [
        58
      ],
      "_idPath": [
        58
      ],
      "_meta": [
        43
      ],
      "_searchKey": [
        58
      ],
      "_slug": [
        58
      ],
      "_slugPath": [
        58
      ],
      "_sys": [
        13
      ],
      "_title": [
        58
      ],
      "item": [
        2
      ],
      "items": [
        2
      ],
      "__typename": [
        58
      ]
    },
    "categoriesItem_AsList": {
      "_analyticsKey": [
        58,
        {
          "scope": [
            0
          ]
        }
      ],
      "_dashboardUrl": [
        58
      ],
      "_id": [
        58
      ],
      "_idPath": [
        58
      ],
      "_meta": [
        43
      ],
      "_searchKey": [
        58
      ],
      "_slug": [
        58
      ],
      "_slugPath": [
        58
      ],
      "_sys": [
        13
      ],
      "_title": [
        58
      ],
      "item": [
        27
      ],
      "items": [
        27
      ],
      "__typename": [
        58
      ]
    },
    "legalPagesItem_AsList": {
      "_analyticsKey": [
        58,
        {
          "scope": [
            0
          ]
        }
      ],
      "_dashboardUrl": [
        58
      ],
      "_id": [
        58
      ],
      "_idPath": [
        58
      ],
      "_meta": [
        43
      ],
      "_searchKey": [
        58
      ],
      "_slug": [
        58
      ],
      "_slugPath": [
        58
      ],
      "_sys": [
        13
      ],
      "_title": [
        58
      ],
      "item": [
        39
      ],
      "items": [
        39
      ],
      "__typename": [
        58
      ]
    },
    "postsItem_AsList": {
      "_analyticsKey": [
        58,
        {
          "scope": [
            0
          ]
        }
      ],
      "_dashboardUrl": [
        58
      ],
      "_id": [
        58
      ],
      "_idPath": [
        58
      ],
      "_meta": [
        43
      ],
      "_searchKey": [
        58
      ],
      "_slug": [
        58
      ],
      "_slugPath": [
        58
      ],
      "_sys": [
        13
      ],
      "_title": [
        58
      ],
      "item": [
        49
      ],
      "items": [
        49
      ],
      "__typename": [
        58
      ]
    }
  }
};

// .basehub/schema.ts
function fragmentOn(name, fields) {
  return { __fragmentOn: name, ...fields };
}
function fragmentOnRecursiveCollection(name, fields, options) {
  let current = {
    ...fields
  };
  if (options.levels > 0) {
    current[options.recursiveKey] = {
      ...options.getLevelArgs ? { __args: options.getLevelArgs(options.levels) } : {},
      items: fragmentOnRecursiveCollection(name, fields, {
        ...options,
        levels: options.levels - 1
      })
    };
  }
  return current;
}

// .basehub/index.ts
var typeMap = linkTypeMap(types_default);
var createClient2 = function(options) {
  const { url, headers } = getStuffFromEnv(options);
  return createClient({
    url: url.toString(),
    ...options,
    headers: { ...options?.headers, ...headers },
    queryRoot: typeMap.Query,
    mutationRoot: typeMap.Mutation,
    subscriptionRoot: typeMap.Subscription
  });
};
var everything = {
  __scalar: true
};
var generateQueryOp = function(fields) {
  return generateGraphqlOperation("query", typeMap.Query, fields);
};
var generateMutationOp = function(fields) {
  return generateGraphqlOperation("mutation", typeMap.Mutation, fields);
};
var getStuffFromEnv = (options) => {
  const defaultEnvVarPrefix = "BASEHUB";
  options = options || {};
  if (options.token === void 0) {
    options.token = null;
  }
  if (options.prefix === void 0) {
    options.prefix = null;
  }
  if (!options.draft && true) {
    options.draft = true;
  }
  const buildEnvVarName = (name) => {
    let prefix = defaultEnvVarPrefix;
    if (options.prefix) {
      if (options.prefix.endsWith("_")) {
        options.prefix = options.prefix.slice(0, -1);
      }
      if (options.prefix.endsWith(name)) {
        options.prefix = options.prefix.slice(0, -name.length);
      }
      if (options.prefix.endsWith(defaultEnvVarPrefix)) {
        prefix = options.prefix;
      } else {
        prefix = `${options.prefix}_${defaultEnvVarPrefix}`;
      }
    }
    return `${prefix}_${name}`;
  };
  const getEnvVar = (name) => {
    if (typeof process === "undefined") {
      return void 0;
    }
    return process?.env?.[buildEnvVarName(name)];
  };
  const parsedDebugForcedURL = getEnvVar("DEBUG_FORCED_URL");
  const parsedBackwardsCompatURL = getEnvVar("URL");
  const backwardsCompatURL = parsedBackwardsCompatURL ? new URL(parsedBackwardsCompatURL) : void 0;
  const basehubUrl = new URL(
    parsedDebugForcedURL ? parsedDebugForcedURL : `https://api.basehub.com/graphql`
  );
  const parsedBasehubTokenEnv = getEnvVar("TOKEN");
  const parsedBasehubRefEnv = getEnvVar("REF");
  const parsedBasehubDraftEnv = getEnvVar("DRAFT");
  const parsedBasehubApiVersionEnv = getEnvVar("API_VERSION");
  let tokenNotFoundErrorMessage = `\u{1F534} Token not found. Make sure to include the ${buildEnvVarName(
    "TOKEN"
  )} env var.`;
  const resolveTokenParam = (token2) => {
    if (!token2)
      return null;
    const isRaw = token2.startsWith("bshb_");
    if (isRaw)
      return token2;
    tokenNotFoundErrorMessage = `\u{1F534} Token not found. Make sure to include the ${token2} env var.`;
    if (typeof process === "undefined") {
      return void 0;
    }
    return process?.env?.[token2] ?? "";
  };
  const resolvedToken = resolveTokenParam(options?.token ?? null);
  const token = resolvedToken ?? basehubUrl.searchParams.get("token") ?? parsedBasehubTokenEnv ?? (backwardsCompatURL ? backwardsCompatURL.searchParams.get("token") : void 0) ?? null;
  if (!token) {
    throw new Error(tokenNotFoundErrorMessage);
  }
  let draft = basehubUrl.searchParams.get("draft") ?? parsedBasehubDraftEnv ?? (backwardsCompatURL ? backwardsCompatURL.searchParams.get("draft") : void 0) ?? false;
  if (options?.draft !== void 0) {
    draft = options.draft;
  }
  let apiVersion = basehubUrl.searchParams.get("api-version") ?? parsedBasehubApiVersionEnv ?? (backwardsCompatURL ? backwardsCompatURL.searchParams.get("api-version") : void 0) ?? "4";
  if (options?.apiVersion !== void 0) {
    apiVersion = options.apiVersion;
  }
  if (basehubUrl.pathname.split("/")[1] !== "graphql") {
    throw new Error(`\u{1F534} Invalid URL. The URL needs to point your repo's GraphQL endpoint, so the pathname should end with /graphql.`);
  }
  basehubUrl.searchParams.delete("token");
  basehubUrl.searchParams.delete("ref");
  basehubUrl.searchParams.delete("draft");
  basehubUrl.searchParams.delete("api-version");
  const gitBranch = "main";
  const gitCommitSHA = "f6faaef6fe38c6584b3950735306270c57e48629";
  const sdkBuildId2 = "bshb_sdk_042f66334af42";
  return {
    isForcedDraft: true,
    draft,
    url: basehubUrl,
    sdkBuildId: sdkBuildId2,
    headers: {
      "x-basehub-token": token,
      "x-basehub-ref": options?.ref ?? resolvedRef.ref,
      "x-basehub-sdk-build-id": sdkBuildId2,
      ...gitBranch ? { "x-basehub-git-branch": gitBranch } : {},
      ...gitCommitSHA ? { "x-basehub-git-commit-sha": gitCommitSHA } : {},
      ...gitBranchDeploymentURL ? { "x-basehub-git-branch-deployment-url": gitBranchDeploymentURL } : {},
      ...productionDeploymentURL ? { "x-basehub-production-deployment-url": productionDeploymentURL } : {},
      ...draft ? { "x-basehub-draft": "true" } : {},
      ...apiVersion ? { "x-basehub-api-version": apiVersion } : {}
    }
  };
};
var sdkBuildId = "bshb_sdk_042f66334af42";
var resolvedRef = { "repoHash": "57ec52db", "type": "branch", "ref": "main", "createSuggestedBranchLink": null, "id": "KluwvFPvKCxusUOmSQG4q", "name": "main", "git": null, "createdAt": "2025-06-16T00:30:26.760Z", "archivedAt": null, "archivedBy": null, "headCommitId": "qNNz4p8JMipdRXk4579YJ", "isDefault": true, "deletedAt": null, "workingRootBlockId": "a8Oul5Re6jsffvG4Ab5XZ" };
var gitBranchDeploymentURL = null;
var productionDeploymentURL = null;
var isNextjs = true;
function hashObject(obj) {
  const sortObjectKeys = (obj2) => {
    if (!isObjectAsWeCommonlyCallIt(obj2))
      return obj2;
    return Object.keys(obj2).sort().reduce((acc, key) => {
      acc[key] = obj2[key];
      return acc;
    }, {});
  };
  const recursiveSortObjectKeys = (obj2) => {
    const sortedObj2 = sortObjectKeys(obj2);
    if (!isObjectAsWeCommonlyCallIt(sortedObj2))
      return sortedObj2;
    Object.keys(sortedObj2).forEach((key) => {
      if (isObjectAsWeCommonlyCallIt(sortedObj2[key])) {
        sortedObj2[key] = recursiveSortObjectKeys(
          sortedObj2[key]
        );
      } else if (Array.isArray(sortedObj2[key])) {
        sortedObj2[key] = sortedObj2[key].map(
          (item) => {
            if (isObjectAsWeCommonlyCallIt(item)) {
              return recursiveSortObjectKeys(item);
            } else {
              return item;
            }
          }
        );
      }
    });
    return sortedObj2;
  };
  const isObjectAsWeCommonlyCallIt = (obj2) => {
    return Object.prototype.toString.call(obj2) === "[object Object]";
  };
  const sortedObj = recursiveSortObjectKeys(obj);
  const str = JSON.stringify(sortedObj);
  let hash = 0;
  for (let i = 0, len = str.length; i < len; i++) {
    let chr = str.charCodeAt(i);
    hash = (hash << 5) - hash + chr;
    hash |= 0;
  }
  return Math.abs(hash).toString();
}
function cacheTagFromQuery(query, apiVersion) {
  const now = performance.now();
  const result = "basehub-" + hashObject({ ...query, refId: resolvedRef.id, ...apiVersion ? { apiVersion } : {} });
  return result;
}
var basehub = (options) => {
  const { url, headers } = getStuffFromEnv(options);
  if (!options) {
    options = {};
  }
  options.getExtraFetchOptions = async (op, _body, originalRequest) => {
    if (op !== "query")
      return {};
    let extra = {
      headers: {
        "x-basehub-sdk-build-id": sdkBuildId
      }
    };
    let isNextjsDraftMode = false;
    if (options.draft === void 0) {
      try {
        const { draftMode } = await import("next/headers");
        isNextjsDraftMode = (await draftMode()).isEnabled;
      } catch (error) {
      }
    }
    const isDraftResolved = true;
    if (isDraftResolved) {
      extra.headers = { ...extra.headers, "x-basehub-draft": "true" };
      extra.next = { revalidate: void 0 };
      extra.cache = "no-store";
      try {
        const { cookies } = await import("next/headers");
        const cookieStore = await cookies();
        const ref = cookieStore.get("bshb-preview-ref-" + resolvedRef.repoHash)?.value;
        if (ref) {
          extra.headers = {
            ...extra.headers,
            "x-basehub-ref": ref
          };
        }
      } catch (error) {
      }
    }
    if (isDraftResolved)
      return extra;
    if (typeof options?.next === "undefined") {
      let isNextjs2 = false;
      try {
        isNextjs2 = !!await import("next/headers");
      } catch (error) {
      }
      if (isNextjs2) {
        const cacheTag = cacheTagFromQuery(originalRequest, headers["x-basehub-api-version"]);
        extra.next = { tags: [cacheTag] };
        extra.headers = {
          ...extra.headers,
          "x-basehub-cache-tag": cacheTag
        };
      }
    }
    return extra;
  };
  return {
    ...createClient2(options),
    raw: createFetcher({ ...options, url, headers })
  };
};
basehub.replaceSystemAliases = createClient.replaceSystemAliases;
export {
  GenqlError,
  basehub,
  cacheTagFromQuery,
  everything,
  fragmentOn,
  fragmentOnRecursiveCollection,
  generateMutationOp,
  generateQueryOp,
  getStuffFromEnv,
  gitBranchDeploymentURL,
  isNextjs,
  productionDeploymentURL,
  resolvedRef,
  sdkBuildId
};
