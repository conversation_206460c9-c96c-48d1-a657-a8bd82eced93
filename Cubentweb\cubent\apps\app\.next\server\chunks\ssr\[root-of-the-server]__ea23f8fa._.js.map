{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/hooks/use-mobile.ts"], "sourcesContent": ["import * as React from \"react\"\n\nconst MO<PERSON>LE_BREAKPOINT = 768\n\nexport function useIsMobile() {\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\n\n  React.useEffect(() => {\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\n    const onChange = () => {\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    }\n    mql.addEventListener(\"change\", onChange)\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    return () => mql.removeEventListener(\"change\", onChange)\n  }, [])\n\n  return !!isMobile\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,oTAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;QACvE,MAAM,WAAW;YACf,YAAY,OAAO,UAAU,GAAG;QAClC;QACA,IAAI,gBAAgB,CAAC,UAAU;QAC/B,YAAY,OAAO,UAAU,GAAG;QAChC,OAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;IACjD,GAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6VAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator-root\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,6VAAC,4QAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,6VAAC,+QAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6VAAC,+QAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,6VAAC,+QAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6VAAC,+QAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6VAAC,+QAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,6VAAC;;0BACC,6VAAC;;;;;0BACD,6VAAC,+QAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6VAAC,+QAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6VAAC,oRAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6VAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6VAAC,+QAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6VAAC,+QAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"skeleton\"\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { VariantProps, cva } from \"class-variance-authority\"\nimport { PanelLeftIcon } from \"lucide-react\"\n\nimport { useIsMobile } from \"@repo/design-system/hooks/use-mobile\"\nimport { cn } from \"@repo/design-system/lib/utils\"\nimport { Button } from \"@repo/design-system/components/ui/button\"\nimport { Input } from \"@repo/design-system/components/ui/input\"\nimport { Separator } from \"@repo/design-system/components/ui/separator\"\nimport {\n  Sheet,\n  SheetContent,\n  SheetDescription,\n  SheetHeader,\n  SheetTitle,\n} from \"@repo/design-system/components/ui/sheet\"\nimport { Skeleton } from \"@repo/design-system/components/ui/skeleton\"\nimport {\n  Tooltip,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"@repo/design-system/components/ui/tooltip\"\n\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\"\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7\nconst SIDEBAR_WIDTH = \"14rem\"\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\"\nconst SIDEBAR_WIDTH_ICON = \"3rem\"\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\"\n\ntype SidebarContextProps = {\n  state: \"expanded\" | \"collapsed\"\n  open: boolean\n  setOpen: (open: boolean) => void\n  openMobile: boolean\n  setOpenMobile: (open: boolean) => void\n  isMobile: boolean\n  toggleSidebar: () => void\n}\n\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null)\n\nfunction useSidebar() {\n  const context = React.useContext(SidebarContext)\n  if (!context) {\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\")\n  }\n\n  return context\n}\n\nfunction SidebarProvider({\n  defaultOpen = true,\n  open: openProp,\n  onOpenChange: setOpenProp,\n  className,\n  style,\n  children,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  defaultOpen?: boolean\n  open?: boolean\n  onOpenChange?: (open: boolean) => void\n}) {\n  const isMobile = useIsMobile()\n  const [openMobile, setOpenMobile] = React.useState(false)\n\n  // This is the internal state of the sidebar.\n  // We use openProp and setOpenProp for control from outside the component.\n  const [_open, _setOpen] = React.useState(defaultOpen)\n  const open = openProp ?? _open\n  const setOpen = React.useCallback(\n    (value: boolean | ((value: boolean) => boolean)) => {\n      const openState = typeof value === \"function\" ? value(open) : value\n      if (setOpenProp) {\n        setOpenProp(openState)\n      } else {\n        _setOpen(openState)\n      }\n\n      // This sets the cookie to keep the sidebar state.\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`\n    },\n    [setOpenProp, open]\n  )\n\n  // Helper to toggle the sidebar.\n  const toggleSidebar = React.useCallback(() => {\n    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open)\n  }, [isMobile, setOpen, setOpenMobile])\n\n  // Adds a keyboard shortcut to toggle the sidebar.\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\n        (event.metaKey || event.ctrlKey)\n      ) {\n        event.preventDefault()\n        toggleSidebar()\n      }\n    }\n\n    window.addEventListener(\"keydown\", handleKeyDown)\n    return () => window.removeEventListener(\"keydown\", handleKeyDown)\n  }, [toggleSidebar])\n\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\n  // This makes it easier to style the sidebar with Tailwind classes.\n  const state = open ? \"expanded\" : \"collapsed\"\n\n  const contextValue = React.useMemo<SidebarContextProps>(\n    () => ({\n      state,\n      open,\n      setOpen,\n      isMobile,\n      openMobile,\n      setOpenMobile,\n      toggleSidebar,\n    }),\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\n  )\n\n  return (\n    <SidebarContext.Provider value={contextValue}>\n      <TooltipProvider delayDuration={0}>\n        <div\n          data-slot=\"sidebar-wrapper\"\n          style={\n            {\n              \"--sidebar-width\": SIDEBAR_WIDTH,\n              \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\n              ...style,\n            } as React.CSSProperties\n          }\n          className={cn(\n            \"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\",\n            className\n          )}\n          {...props}\n        >\n          {children}\n        </div>\n      </TooltipProvider>\n    </SidebarContext.Provider>\n  )\n}\n\nfunction Sidebar({\n  side = \"left\",\n  variant = \"sidebar\",\n  collapsible = \"offcanvas\",\n  className,\n  children,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  side?: \"left\" | \"right\"\n  variant?: \"sidebar\" | \"floating\" | \"inset\"\n  collapsible?: \"offcanvas\" | \"icon\" | \"none\"\n}) {\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar()\n\n  if (collapsible === \"none\") {\n    return (\n      <div\n        data-slot=\"sidebar\"\n        className={cn(\n          \"bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n\n  if (isMobile) {\n    return (\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\n        <SheetContent\n          data-sidebar=\"sidebar\"\n          data-slot=\"sidebar\"\n          data-mobile=\"true\"\n          className=\"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden\"\n          style={\n            {\n              \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\n            } as React.CSSProperties\n          }\n          side={side}\n        >\n          <SheetHeader className=\"sr-only\">\n            <SheetTitle>Sidebar</SheetTitle>\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\n          </SheetHeader>\n          <div className=\"flex h-full w-full flex-col\">{children}</div>\n        </SheetContent>\n      </Sheet>\n    )\n  }\n\n  return (\n    <div\n      className=\"group peer text-sidebar-foreground hidden md:block\"\n      data-state={state}\n      data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\n      data-variant={variant}\n      data-side={side}\n      data-slot=\"sidebar\"\n    >\n      {/* This is what handles the sidebar gap on desktop */}\n      <div\n        data-slot=\"sidebar-gap\"\n        className={cn(\n          \"relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear\",\n          \"group-data-[collapsible=offcanvas]:w-0\",\n          \"group-data-[side=right]:rotate-180\",\n          variant === \"floating\" || variant === \"inset\"\n            ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]\"\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon)\"\n        )}\n      />\n      <div\n        data-slot=\"sidebar-container\"\n        className={cn(\n          \"fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex\",\n          side === \"left\"\n            ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\n            : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\n          // Adjust the padding for floating and inset variants.\n          variant === \"floating\" || variant === \"inset\"\n            ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]\"\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l\",\n          className\n        )}\n        {...props}\n      >\n        <div\n          data-sidebar=\"sidebar\"\n          data-slot=\"sidebar-inner\"\n          className=\"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\"\n        >\n          {children}\n        </div>\n      </div>\n    </div>\n  )\n}\n\nfunction SidebarTrigger({\n  className,\n  onClick,\n  ...props\n}: React.ComponentProps<typeof Button>) {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <Button\n      data-sidebar=\"trigger\"\n      data-slot=\"sidebar-trigger\"\n      variant=\"ghost\"\n      size=\"icon\"\n      className={cn(\"size-7\", className)}\n      onClick={(event) => {\n        onClick?.(event)\n        toggleSidebar()\n      }}\n      {...props}\n    >\n      <PanelLeftIcon />\n      <span className=\"sr-only\">Toggle Sidebar</span>\n    </Button>\n  )\n}\n\nfunction SidebarRail({ className, ...props }: React.ComponentProps<\"button\">) {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <button\n      data-sidebar=\"rail\"\n      data-slot=\"sidebar-rail\"\n      aria-label=\"Toggle Sidebar\"\n      tabIndex={-1}\n      onClick={toggleSidebar}\n      title=\"Toggle Sidebar\"\n      className={cn(\n        \"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex\",\n        \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\",\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\n        \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\",\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarInset({ className, ...props }: React.ComponentProps<\"main\">) {\n  return (\n    <main\n      data-slot=\"sidebar-inset\"\n      className={cn(\n        \"bg-background relative flex w-full flex-1 flex-col\",\n        \"md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarInput({\n  className,\n  ...props\n}: React.ComponentProps<typeof Input>) {\n  return (\n    <Input\n      data-slot=\"sidebar-input\"\n      data-sidebar=\"input\"\n      className={cn(\"bg-background h-8 w-full shadow-none\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-header\"\n      data-sidebar=\"header\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-footer\"\n      data-sidebar=\"footer\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof Separator>) {\n  return (\n    <Separator\n      data-slot=\"sidebar-separator\"\n      data-sidebar=\"separator\"\n      className={cn(\"bg-sidebar-border mx-2 w-auto\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-content\"\n      data-sidebar=\"content\"\n      className={cn(\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-group\"\n      data-sidebar=\"group\"\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroupLabel({\n  className,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"div\"> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"div\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-group-label\"\n      data-sidebar=\"group-label\"\n      className={cn(\n        \"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroupAction({\n  className,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-group-action\"\n      data-sidebar=\"group-action\"\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 md:after:hidden\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroupContent({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-group-content\"\n      data-sidebar=\"group-content\"\n      className={cn(\"w-full text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<\"ul\">) {\n  return (\n    <ul\n      data-slot=\"sidebar-menu\"\n      data-sidebar=\"menu\"\n      className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<\"li\">) {\n  return (\n    <li\n      data-slot=\"sidebar-menu-item\"\n      data-sidebar=\"menu-item\"\n      className={cn(\"group/menu-item relative\", className)}\n      {...props}\n    />\n  )\n}\n\nconst sidebarMenuButtonVariants = cva(\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\n        outline:\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\n      },\n      size: {\n        default: \"h-8 text-sm\",\n        sm: \"h-7 text-xs\",\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction SidebarMenuButton({\n  asChild = false,\n  isActive = false,\n  variant = \"default\",\n  size = \"default\",\n  tooltip,\n  className,\n  ...props\n}: React.ComponentProps<\"button\"> & {\n  asChild?: boolean\n  isActive?: boolean\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\n  const Comp = asChild ? Slot : \"button\"\n  const { isMobile, state } = useSidebar()\n\n  const button = (\n    <Comp\n      data-slot=\"sidebar-menu-button\"\n      data-sidebar=\"menu-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\n      {...props}\n    />\n  )\n\n  if (!tooltip) {\n    return button\n  }\n\n  if (typeof tooltip === \"string\") {\n    tooltip = {\n      children: tooltip,\n    }\n  }\n\n  return (\n    <Tooltip>\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\n      <TooltipContent\n        side=\"right\"\n        align=\"center\"\n        hidden={state !== \"collapsed\" || isMobile}\n        {...tooltip}\n      />\n    </Tooltip>\n  )\n}\n\nfunction SidebarMenuAction({\n  className,\n  asChild = false,\n  showOnHover = false,\n  ...props\n}: React.ComponentProps<\"button\"> & {\n  asChild?: boolean\n  showOnHover?: boolean\n}) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-menu-action\"\n      data-sidebar=\"menu-action\"\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 md:after:hidden\",\n        \"peer-data-[size=sm]/menu-button:top-1\",\n        \"peer-data-[size=default]/menu-button:top-1.5\",\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        showOnHover &&\n          \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuBadge({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-menu-badge\"\n      data-sidebar=\"menu-badge\"\n      className={cn(\n        \"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\",\n        \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\n        \"peer-data-[size=sm]/menu-button:top-1\",\n        \"peer-data-[size=default]/menu-button:top-1.5\",\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuSkeleton({\n  className,\n  showIcon = false,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  showIcon?: boolean\n}) {\n  // Random width between 50 to 90%.\n  const width = React.useMemo(() => {\n    return `${Math.floor(Math.random() * 40) + 50}%`\n  }, [])\n\n  return (\n    <div\n      data-slot=\"sidebar-menu-skeleton\"\n      data-sidebar=\"menu-skeleton\"\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\n      {...props}\n    >\n      {showIcon && (\n        <Skeleton\n          className=\"size-4 rounded-md\"\n          data-sidebar=\"menu-skeleton-icon\"\n        />\n      )}\n      <Skeleton\n        className=\"h-4 max-w-(--skeleton-width) flex-1\"\n        data-sidebar=\"menu-skeleton-text\"\n        style={\n          {\n            \"--skeleton-width\": width,\n          } as React.CSSProperties\n        }\n      />\n    </div>\n  )\n}\n\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<\"ul\">) {\n  return (\n    <ul\n      data-slot=\"sidebar-menu-sub\"\n      data-sidebar=\"menu-sub\"\n      className={cn(\n        \"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuSubItem({\n  className,\n  ...props\n}: React.ComponentProps<\"li\">) {\n  return (\n    <li\n      data-slot=\"sidebar-menu-sub-item\"\n      data-sidebar=\"menu-sub-item\"\n      className={cn(\"group/menu-sub-item relative\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuSubButton({\n  asChild = false,\n  size = \"md\",\n  isActive = false,\n  className,\n  ...props\n}: React.ComponentProps<\"a\"> & {\n  asChild?: boolean\n  size?: \"sm\" | \"md\"\n  isActive?: boolean\n}) {\n  const Comp = asChild ? Slot : \"a\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-menu-sub-button\"\n      data-sidebar=\"menu-sub-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\n        size === \"sm\" && \"text-xs\",\n        size === \"md\" && \"text-sm\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sidebar,\n  SidebarContent,\n  SidebarFooter,\n  SidebarGroup,\n  SidebarGroupAction,\n  SidebarGroupContent,\n  SidebarGroupLabel,\n  SidebarHeader,\n  SidebarInput,\n  SidebarInset,\n  SidebarMenu,\n  SidebarMenuAction,\n  SidebarMenuBadge,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  SidebarMenuSkeleton,\n  SidebarMenuSub,\n  SidebarMenuSubButton,\n  SidebarMenuSubItem,\n  SidebarProvider,\n  SidebarRail,\n  SidebarSeparator,\n  SidebarTrigger,\n  useSidebar,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AApBA;;;;;;;;;;;;;;AA2BA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,CAAA,GAAA,oTAAA,CAAA,gBAAmB,AAAD,EAA8B;AAEvE,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,oTAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,SAAS,gBAAgB,EACvB,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAKJ;IACC,MAAM,WAAW,CAAA,GAAA,sJAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAc,AAAD,EAAE;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,CAAA,GAAA,oTAAA,CAAA,cAAiB,AAAD,EAC9B,CAAC;QACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;QAC9D,IAAI,aAAa;YACf,YAAY;QACd,OAAO;YACL,SAAS;QACX;QAEA,kDAAkD;QAClD,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,UAAU,kBAAkB,EAAE,wBAAwB;IACpG,GACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,oTAAA,CAAA,cAAiB,AAAD,EAAE;QACtC,OAAO,WAAW,cAAc,CAAC,OAAS,CAAC,QAAQ,QAAQ,CAAC,OAAS,CAAC;IACxE,GAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,CAAA,GAAA,oTAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,gBAAgB,CAAC;YACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;gBACA,MAAM,cAAc;gBACpB;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,CAAA,GAAA,oTAAA,CAAA,UAAa,AAAD,EAC/B,IAAM,CAAC;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,6VAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,6VAAC,4JAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,6VAAC;gBACC,aAAU;gBACV,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,mFACA;gBAED,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,QAAQ,EACf,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OAKJ;IACC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,6VAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAED,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,6VAAC,0JAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,6VAAC,0JAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,aAAU;gBACV,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;;kCAEN,6VAAC,0JAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6VAAC,0JAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,6VAAC,0JAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAEpB,6VAAC;wBAAI,WAAU;kCAA+B;;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,6VAAC;QACC,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;QACX,aAAU;;0BAGV,6VAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,2FACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,qFACA;;;;;;0BAGR,6VAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,6FACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,6VAAC;oBACC,gBAAa;oBACb,aAAU;oBACV,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,OAAO,EACP,GAAG,OACiC;IACpC,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6VAAC,2JAAA,CAAA,SAAM;QACL,gBAAa;QACb,aAAU;QACV,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,6VAAC,wSAAA,CAAA,gBAAa;;;;;0BACd,6VAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAuC;IAC1E,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6VAAC;QACC,gBAAa;QACb,aAAU;QACV,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,mPACA,4EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAqC;IACzE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,sDACA,mNACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACgC;IACnC,qBACE,6VAAC,0JAAA,CAAA,QAAK;QACJ,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACoC;IACvC,qBACE,6VAAC,8JAAA,CAAA,YAAS;QACR,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC1E,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACiD;IACpD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,4OACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACoD;IACvD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,8RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAmC;IACtE,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAmC;IAC1E,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,MAAM,4BAA4B,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EAClC,qzBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,kBAAkB,EACzB,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OAK6C;IAChD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,MAAM,uBACJ,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;YAAE;YAAS;QAAK,IAAI;QAC3D,GAAG,KAAK;;;;;;IAIb,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,6VAAC,4JAAA,CAAA,UAAO;;0BACN,6VAAC,4JAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,6VAAC,4JAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ,UAAU,eAAe;gBAChC,GAAG,OAAO;;;;;;;;;;;;AAInB;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,cAAc,KAAK,EACnB,GAAG,OAIJ;IACC,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,oVACA,kDAAkD;QAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,WAAW,KAAK,EAChB,GAAG,OAGJ;IACC,kCAAkC;IAClC,MAAM,QAAQ,CAAA,GAAA,oTAAA,CAAA,UAAa,AAAD,EAAE;QAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;IAClD,GAAG,EAAE;IAEL,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,6VAAC,6JAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,6VAAC,6JAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;QAC7C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,UAAU,KAAK,EACf,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,SAAS,EACT,GAAG,OAKJ;IACC,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,ifACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1015, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/notifications/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      KNOCK_SECRET_API_KEY: z.string().optional(),\n    },\n    client: {\n      NEXT_PUBLIC_KNOCK_API_KEY: z.string().optional(),\n      NEXT_PUBLIC_KNOCK_FEED_CHANNEL_ID: z.string().optional(),\n    },\n    runtimeEnv: {\n      NEXT_PUBLIC_KNOCK_API_KEY: process.env.NEXT_PUBLIC_KNOCK_API_KEY,\n      NEXT_PUBLIC_KNOCK_FEED_CHANNEL_ID:\n        process.env.NEXT_PUBLIC_KNOCK_FEED_CHANNEL_ID,\n      KNOCK_SECRET_API_KEY: process.env.KNOCK_SECRET_API_KEY,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,0QAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,sBAAsB,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC3C;QACA,QAAQ;YACN,2BAA2B,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAC9C,mCAAmC,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACxD;QACA,YAAY;YACV,yBAAyB;YACzB,iCAAiC;YAEjC,sBAAsB,QAAQ,GAAG,CAAC,oBAAoB;QACxD;IACF", "debugId": null}}, {"offset": {"line": 1043, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/notifications/components/provider.tsx"], "sourcesContent": ["'use client';\n\nimport { KnockFeedProvider, KnockProvider } from '@knocklabs/react';\nimport type { ReactNode } from 'react';\nimport { keys } from '../keys';\n\nconst knockApiKey = keys().NEXT_PUBLIC_KNOCK_API_KEY;\nconst knockFeedChannelId = keys().NEXT_PUBLIC_KNOCK_FEED_CHANNEL_ID;\n\ntype NotificationsProviderProps = {\n  children: ReactNode;\n  userId: string;\n};\n\nexport const NotificationsProvider = ({\n  children,\n  userId,\n}: NotificationsProviderProps) => {\n  if (!knockApiKey || !knockFeedChannelId) {\n    return children;\n  }\n\n  return (\n    <KnockProvider apiKey={knockApiKey} userId={userId}>\n      <KnockFeedProvider feedId={knockFeedChannelId}>\n        {children}\n      </KnockFeedProvider>\n    </KnockProvider>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAEA;AAJA;;;;AAMA,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,OAAI,AAAD,IAAI,yBAAyB;AACpD,MAAM,qBAAqB,CAAA,GAAA,iIAAA,CAAA,OAAI,AAAD,IAAI,iCAAiC;AAO5D,MAAM,wBAAwB,CAAC,EACpC,QAAQ,EACR,MAAM,EACqB;IAC3B,IAAI,CAAC,eAAe,CAAC,oBAAoB;QACvC,OAAO;IACT;IAEA,qBACE,6VAAC,qTAAA,CAAA,gBAAa;QAAC,QAAQ;QAAa,QAAQ;kBAC1C,cAAA,6VAAC,yTAAA,CAAA,oBAAiB;YAAC,QAAQ;sBACxB;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 1084, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/notifications/components/trigger.tsx"], "sourcesContent": ["'use client';\n\nimport {\n  NotificationFeedPopover,\n  NotificationIconButton,\n} from '@knocklabs/react';\nimport { useRef, useState } from 'react';\nimport type { RefObject } from 'react';\nimport { keys } from '../keys';\n\n// Required CSS import, unless you're overriding the styling\nimport '@knocklabs/react/dist/index.css';\nimport '../styles.css';\n\nexport const NotificationsTrigger = () => {\n  const [isVisible, setIsVisible] = useState(false);\n  const notifButtonRef = useRef<HTMLButtonElement>(null);\n\n  const handleClose = (event: Event) => {\n    if (event.target === notifButtonRef.current) {\n      return;\n    }\n\n    setIsVisible(false);\n  };\n\n  if (!keys().NEXT_PUBLIC_KNOCK_API_KEY) {\n    return null;\n  }\n\n  return (\n    <>\n      <NotificationIconButton\n        onClick={() => setIsVisible(!isVisible)}\n        ref={notifButtonRef}\n      />\n      {notifButtonRef.current && (\n        <NotificationFeedPopover\n          buttonRef={notifButtonRef as RefObject<HTMLElement>}\n          isVisible={isVisible}\n          onClose={handleClose}\n        />\n      )}\n    </>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAIA;AAEA;AARA;;;;;;;AAcO,MAAM,uBAAuB;IAClC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,iBAAiB,CAAA,GAAA,oTAAA,CAAA,SAAM,AAAD,EAAqB;IAEjD,MAAM,cAAc,CAAC;QACnB,IAAI,MAAM,MAAM,KAAK,eAAe,OAAO,EAAE;YAC3C;QACF;QAEA,aAAa;IACf;IAEA,IAAI,CAAC,CAAA,GAAA,iIAAA,CAAA,OAAI,AAAD,IAAI,yBAAyB,EAAE;QACrC,OAAO;IACT;IAEA,qBACE;;0BACE,6VAAC,mVAAA,CAAA,yBAAsB;gBACrB,SAAS,IAAM,aAAa,CAAC;gBAC7B,KAAK;;;;;;YAEN,eAAe,OAAO,kBACrB,6VAAC,qVAAA,CAAA,0BAAuB;gBACtB,WAAW;gBACX,WAAW;gBACX,SAAS;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 1161, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/app/app/%28authenticated%29/components/logo.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 24, height: 24, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,sJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 1180, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/app/app/%28authenticated%29/components/app-header.tsx"], "sourcesContent": ["'use client';\n\nimport { But<PERSON> } from '@repo/design-system/components/ui/button';\nimport { UserButton } from '@repo/auth/client';\nimport { NotificationsTrigger } from '@repo/notifications/components/trigger';\nimport { useSidebar } from '@repo/design-system/components/ui/sidebar';\nimport { Menu } from 'lucide-react';\nimport Image from 'next/image';\nimport Link from 'next/link';\n\nimport Logo from './logo.svg';\n\nexport const AppHeader = () => {\n  const { toggleSidebar } = useSidebar();\n  \n  return (\n    <header className=\"sticky top-0 left-0 z-50 w-full bg-sidebar backdrop-blur-sm border-b border-sidebar-border supports-[backdrop-filter]:bg-sidebar/95\">\n      <div className=\"flex h-14 items-center justify-between px-4\">\n        {/* Left side - Mobile Menu Button + Logo */}\n        <div className=\"flex items-center gap-2\">\n          {/* Mobile Menu Button - Only visible on mobile */}\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"md:hidden shrink-0\"\n            onClick={toggleSidebar}\n          >\n            <Menu className=\"h-4 w-4\" />\n          </Button>\n          \n          <Link href=\"/dashboard\" className=\"md:hidden flex items-center gap-2 hover:opacity-80 transition-opacity\">\n            <Image\n              src={Logo}\n              alt=\"Cubent Logo\"\n              width={32}\n              height={32}\n              className=\"dark:invert\"\n            />\n            <span className=\"font-medium text-base\">Cubent</span>\n          </Link>\n        </div>\n\n        {/* Right side - User menu and notifications */}\n        <div className=\"flex items-center gap-3\">\n          {/* Notifications */}\n          <Button variant=\"ghost\" size=\"icon\" className=\"shrink-0\">\n            <div className=\"h-4 w-4\">\n              <NotificationsTrigger />\n            </div>\n          </Button>\n\n          {/* User Button */}\n          <UserButton\n            showName\n            appearance={{\n              elements: {\n                rootBox: 'flex overflow-hidden',\n                userButtonBox: 'flex-row-reverse',\n                userButtonOuterIdentifier: 'truncate pl-0 !text-white',\n                userPreviewMainIdentifier: '!text-white',\n                userPreviewSecondaryIdentifier: '!text-white/90',\n                userButtonTrigger: '!text-white',\n              },\n            }}\n          />\n        </div>\n      </div>\n    </header>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AAVA;;;;;;;;;;AAYO,MAAM,YAAY;IACvB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD;IAEnC,qBACE,6VAAC;QAAO,WAAU;kBAChB,cAAA,6VAAC;YAAI,WAAU;;8BAEb,6VAAC;oBAAI,WAAU;;sCAEb,6VAAC,2JAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS;sCAET,cAAA,6VAAC,sRAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAGlB,6VAAC,2QAAA,CAAA,UAAI;4BAAC,MAAK;4BAAa,WAAU;;8CAChC,6VAAC,4OAAA,CAAA,UAAK;oCACJ,KAAK,0VAAA,CAAA,UAAI;oCACT,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;8CAEZ,6VAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;8BAK5C,6VAAC;oBAAI,WAAU;;sCAEb,6VAAC,2JAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAO,WAAU;sCAC5C,cAAA,6VAAC;gCAAI,WAAU;0CACb,cAAA,6VAAC,mJAAA,CAAA,uBAAoB;;;;;;;;;;;;;;;sCAKzB,6VAAC,qRAAA,CAAA,aAAU;4BACT,QAAQ;4BACR,YAAY;gCACV,UAAU;oCACR,SAAS;oCACT,eAAe;oCACf,2BAA2B;oCAC3B,2BAA2B;oCAC3B,gCAAgC;oCAChC,mBAAmB;gCACrB;4BACF;;;;;;;;;;;;;;;;;;;;;;;AAMZ", "debugId": null}}, {"offset": {"line": 1330, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/app/app/%28authenticated%29/components/posthog-identifier.tsx"], "sourcesContent": ["'use client';\n\nimport { useAnalytics } from '@repo/analytics/posthog/client';\nimport { useUser } from '@repo/auth/client';\nimport { usePathname, useSearchParams } from 'next/navigation';\nimport { useEffect, useRef } from 'react';\n\nexport const PostHogIdentifier = () => {\n  const { user } = useUser();\n  const identified = useRef(false);\n  const pathname = usePathname();\n  const searchParams = useSearchParams();\n  const analytics = useAnalytics();\n\n  useEffect(() => {\n    // Track pageviews\n    if (pathname && analytics) {\n      let url = window.origin + pathname;\n      if (searchParams.toString()) {\n        url = `${url}?${searchParams.toString()}`;\n      }\n      analytics.capture('$pageview', {\n        $current_url: url,\n      });\n    }\n  }, [pathname, searchParams, analytics]);\n\n  useEffect(() => {\n    if (!user || identified.current) {\n      return;\n    }\n\n    analytics.identify(user.id, {\n      email: user.emailAddresses.at(0)?.emailAddress,\n      firstName: user.firstName,\n      lastName: user.lastName,\n      createdAt: user.createdAt,\n      avatar: user.imageUrl,\n      phoneNumber: user.phoneNumbers.at(0)?.phoneNumber,\n    });\n\n    identified.current = true;\n  }, [user, analytics]);\n\n  return null;\n};\n"], "names": [], "mappings": ";;;AAEA;AAAA;AACA;AAAA;AACA;AACA;AALA;;;;;AAOO,MAAM,oBAAoB;IAC/B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;IACvB,MAAM,aAAa,CAAA,GAAA,oTAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,iPAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,eAAe,CAAA,GAAA,iPAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,YAAY,CAAA,GAAA,yTAAA,CAAA,eAAY,AAAD;IAE7B,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,kBAAkB;QAClB,IAAI,YAAY,WAAW;YACzB,IAAI,MAAM,OAAO,MAAM,GAAG;YAC1B,IAAI,aAAa,QAAQ,IAAI;gBAC3B,MAAM,GAAG,IAAI,CAAC,EAAE,aAAa,QAAQ,IAAI;YAC3C;YACA,UAAU,OAAO,CAAC,aAAa;gBAC7B,cAAc;YAChB;QACF;IACF,GAAG;QAAC;QAAU;QAAc;KAAU;IAEtC,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ,WAAW,OAAO,EAAE;YAC/B;QACF;QAEA,UAAU,QAAQ,CAAC,KAAK,EAAE,EAAE;YAC1B,OAAO,KAAK,cAAc,CAAC,EAAE,CAAC,IAAI;YAClC,WAAW,KAAK,SAAS;YACzB,UAAU,KAAK,QAAQ;YACvB,WAAW,KAAK,SAAS;YACzB,QAAQ,KAAK,QAAQ;YACrB,aAAa,KAAK,YAAY,CAAC,EAAE,CAAC,IAAI;QACxC;QAEA,WAAW,OAAO,GAAG;IACvB,GAAG;QAAC;QAAM;KAAU;IAEpB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1391, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/app/app/%28authenticated%29/components/sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { usePathname } from 'next/navigation';\nimport {\n  Sidebar,\n  SidebarContent,\n  SidebarFooter,\n  SidebarGroup,\n  SidebarGroupContent,\n  SidebarGroupLabel,\n  SidebarHeader,\n  SidebarInset,\n  SidebarMenu,\n\n  SidebarMenuButton,\n  SidebarMenuItem,\n  useSidebar,\n} from '@repo/design-system/components/ui/sidebar';\nimport { cn } from '@repo/design-system/lib/utils';\nimport {\n  AnchorIcon,\n  BookOpenIcon,\n  BotIcon,\n  LayoutDashboardIcon,\n  LifeBuoyIcon,\n  PieChartIcon,\n  SendIcon,\n  Settings2Icon,\n  SquareTerminalIcon,\n  MessageSquareIcon,\n  CreditCardIcon,\n  UserIcon,\n  BellIcon,\n  ShieldIcon,\n  HelpCircleIcon,\n  BarChart3,\n  Activity,\n  Zap,\n  DollarSign,\n  Lock,\n} from 'lucide-react';\nimport Link from 'next/link';\nimport type { ReactNode } from 'react';\nimport { Search } from './search';\n\ntype GlobalSidebarProperties = {\n  readonly children: ReactNode;\n};\n\nconst data = {\n  user: {\n    name: 'shadcn',\n    email: '<EMAIL>',\n    avatar: '/avatars/shadcn.jpg',\n  },\n  navMain: [\n    {\n      title: 'Dashboard',\n      url: '/dashboard',\n      icon: LayoutDashboardIcon,\n    },\n    {\n      title: 'Profile',\n      url: '/profile',\n      icon: UserIcon,\n    },\n  ],\n  navUsageMetrics: [\n    {\n      title: 'Cubent Units',\n      url: '/profile/usage',\n      icon: BarChart3,\n      locked: true,\n      grey: true,\n    },\n    {\n      title: 'Request Tracking',\n      url: '/usage/requests',\n      icon: Activity,\n      locked: false,\n    },\n    {\n      title: 'Token Usage',\n      url: '/usage/tokens',\n      icon: Zap,\n      locked: false,\n    },\n    {\n      title: 'Cost Tracking',\n      url: '/usage/cost',\n      icon: DollarSign,\n      locked: false,\n    },\n  ],\n\n  navSecondary: [\n    {\n      title: 'Documentation',\n      url: 'https://docs.cubent.dev',\n      icon: BookOpenIcon,\n    },\n  ],\n};\n\nexport const GlobalSidebar = ({ children }: GlobalSidebarProperties) => {\n  const pathname = usePathname();\n\n  return (\n    <>\n      <Sidebar variant=\"inset\" className=\"h-full max-h-[calc(100vh-4rem)]\">\n        {/*\n          Logo positioned at header height - Desktop only\n          - Shows Cubent logo and text in sidebar header\n          - Hidden on mobile (header logo shows instead)\n          - Matches header height (h-14) and styling\n          - Same size as header logo (32x32px)\n        */}\n        <div className=\"hidden md:flex items-center gap-2 px-4 py-3 h-14 border-b border-sidebar-border bg-sidebar\">\n          <img src=\"/logo.svg\" alt=\"Cubent\" width=\"32\" height=\"32\" className=\"dark:invert\" />\n          <span className=\"font-medium text-base\">Cubent</span>\n        </div>\n        <SidebarHeader>\n          {/* Organization selector removed */}\n        </SidebarHeader>\n        {/* Search hidden */}\n        <SidebarContent className=\"flex-1 overflow-y-auto\">\n          <SidebarGroup>\n            <SidebarGroupLabel className=\"text-gray-400\">Dashboard</SidebarGroupLabel>\n            <SidebarMenu>\n              {data.navMain.map((item) => (\n                <SidebarMenuItem key={item.title}>\n                  <SidebarMenuButton\n                    asChild\n                    tooltip={item.title}\n                    isActive={pathname === item.url}\n                    size=\"lg\"\n                    className=\"h-12 px-4\"\n                  >\n                    <Link href={item.url}>\n                      <item.icon />\n                      <span>{item.title}</span>\n                    </Link>\n                  </SidebarMenuButton>\n                </SidebarMenuItem>\n              ))}\n            </SidebarMenu>\n          </SidebarGroup>\n\n          <SidebarGroup>\n            <SidebarGroupLabel className=\"text-gray-400\">Usage Metrics</SidebarGroupLabel>\n            <SidebarMenu>\n              {data.navUsageMetrics.map((item) => (\n                <SidebarMenuItem key={item.title}>\n                  <SidebarMenuButton\n                    asChild\n                    tooltip={item.title}\n                    isActive={pathname === item.url}\n                    size=\"lg\"\n                    className={`h-12 px-4 ${item.locked ? 'opacity-50 cursor-not-allowed' : ''} ${item.grey ? 'text-gray-400' : ''}`}\n                  >\n                    {item.locked ? (\n                      <div className=\"flex items-center gap-2 w-full\">\n                        <item.icon />\n                        <span>{item.title}</span>\n                        <Lock className=\"h-3 w-3 ml-auto\" />\n                      </div>\n                    ) : (\n                      <Link href={item.url}>\n                        <item.icon />\n                        <span>{item.title}</span>\n                        {item.grey && (\n                          <Lock className=\"h-3 w-3 ml-auto\" />\n                        )}\n                      </Link>\n                    )}\n                  </SidebarMenuButton>\n                </SidebarMenuItem>\n              ))}\n            </SidebarMenu>\n          </SidebarGroup>\n\n\n\n          <SidebarGroup className=\"mt-auto\">\n            <SidebarGroupContent>\n              <SidebarMenu>\n                {data.navSecondary.map((item) => (\n                  <SidebarMenuItem key={item.title}>\n                    <SidebarMenuButton\n                      asChild\n                      isActive={pathname === item.url}\n                    >\n                      <Link\n                        href={item.url}\n                        {...(item.url.startsWith('http') && {\n                          target: '_blank',\n                          rel: 'noopener noreferrer'\n                        })}\n                      >\n                        <item.icon />\n                        <span>{item.title}</span>\n                      </Link>\n                    </SidebarMenuButton>\n                  </SidebarMenuItem>\n                ))}\n              </SidebarMenu>\n            </SidebarGroupContent>\n          </SidebarGroup>\n        </SidebarContent>\n        <SidebarFooter>\n          {/* User profile moved to header */}\n        </SidebarFooter>\n      </Sidebar>\n      <SidebarInset className=\"flex-1\">\n        {children}\n      </SidebarInset>\n    </>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAgBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsBA;AAzCA;;;;;;AAiDA,MAAM,OAAO;IACX,MAAM;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;IACV;IACA,SAAS;QACP;YACE,OAAO;YACP,KAAK;YACL,MAAM,oTAAA,CAAA,sBAAmB;QAC3B;QACA;YACE,OAAO;YACP,KAAK;YACL,MAAM,0RAAA,CAAA,WAAQ;QAChB;KACD;IACD,iBAAiB;QACf;YACE,OAAO;YACP,KAAK;YACL,MAAM,sSAAA,CAAA,YAAS;YACf,QAAQ;YACR,MAAM;QACR;QACA;YACE,OAAO;YACP,KAAK;YACL,MAAM,8RAAA,CAAA,WAAQ;YACd,QAAQ;QACV;QACA;YACE,OAAO;YACP,KAAK;YAC<PERSON>,MAAM,oRAAA,CAAA,MAAG;YACT,QAAQ;QACV;QACA;YACE,OAAO;YACP,KAAK;YACL,MAAM,sSAAA,CAAA,aAAU;YAChB,QAAQ;QACV;KACD;IAED,cAAc;QACZ;YACE,OAAO;YACP,KAAK;YACL,MAAM,sSAAA,CAAA,eAAY;QACpB;KACD;AACH;AAEO,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAA2B;IACjE,MAAM,WAAW,CAAA,GAAA,iPAAA,CAAA,cAAW,AAAD;IAE3B,qBACE;;0BACE,6VAAC,4JAAA,CAAA,UAAO;gBAAC,SAAQ;gBAAQ,WAAU;;kCAQjC,6VAAC;wBAAI,WAAU;;0CACb,6VAAC;gCAAI,KAAI;gCAAY,KAAI;gCAAS,OAAM;gCAAK,QAAO;gCAAK,WAAU;;;;;;0CACnE,6VAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;kCAE1C,6VAAC,4JAAA,CAAA,gBAAa;;;;;kCAId,6VAAC,4JAAA,CAAA,iBAAc;wBAAC,WAAU;;0CACxB,6VAAC,4JAAA,CAAA,eAAY;;kDACX,6VAAC,4JAAA,CAAA,oBAAiB;wCAAC,WAAU;kDAAgB;;;;;;kDAC7C,6VAAC,4JAAA,CAAA,cAAW;kDACT,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,qBACjB,6VAAC,4JAAA,CAAA,kBAAe;0DACd,cAAA,6VAAC,4JAAA,CAAA,oBAAiB;oDAChB,OAAO;oDACP,SAAS,KAAK,KAAK;oDACnB,UAAU,aAAa,KAAK,GAAG;oDAC/B,MAAK;oDACL,WAAU;8DAEV,cAAA,6VAAC,2QAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,GAAG;;0EAClB,6VAAC,KAAK,IAAI;;;;;0EACV,6VAAC;0EAAM,KAAK,KAAK;;;;;;;;;;;;;;;;;+CAVD,KAAK,KAAK;;;;;;;;;;;;;;;;0CAkBtC,6VAAC,4JAAA,CAAA,eAAY;;kDACX,6VAAC,4JAAA,CAAA,oBAAiB;wCAAC,WAAU;kDAAgB;;;;;;kDAC7C,6VAAC,4JAAA,CAAA,cAAW;kDACT,KAAK,eAAe,CAAC,GAAG,CAAC,CAAC,qBACzB,6VAAC,4JAAA,CAAA,kBAAe;0DACd,cAAA,6VAAC,4JAAA,CAAA,oBAAiB;oDAChB,OAAO;oDACP,SAAS,KAAK,KAAK;oDACnB,UAAU,aAAa,KAAK,GAAG;oDAC/B,MAAK;oDACL,WAAW,CAAC,UAAU,EAAE,KAAK,MAAM,GAAG,kCAAkC,GAAG,CAAC,EAAE,KAAK,IAAI,GAAG,kBAAkB,IAAI;8DAE/G,KAAK,MAAM,iBACV,6VAAC;wDAAI,WAAU;;0EACb,6VAAC,KAAK,IAAI;;;;;0EACV,6VAAC;0EAAM,KAAK,KAAK;;;;;;0EACjB,6VAAC,sRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;6EAGlB,6VAAC,2QAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,GAAG;;0EAClB,6VAAC,KAAK,IAAI;;;;;0EACV,6VAAC;0EAAM,KAAK,KAAK;;;;;;4DAChB,KAAK,IAAI,kBACR,6VAAC,sRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;;;;;;+CAnBJ,KAAK,KAAK;;;;;;;;;;;;;;;;0CA+BtC,6VAAC,4JAAA,CAAA,eAAY;gCAAC,WAAU;0CACtB,cAAA,6VAAC,4JAAA,CAAA,sBAAmB;8CAClB,cAAA,6VAAC,4JAAA,CAAA,cAAW;kDACT,KAAK,YAAY,CAAC,GAAG,CAAC,CAAC,qBACtB,6VAAC,4JAAA,CAAA,kBAAe;0DACd,cAAA,6VAAC,4JAAA,CAAA,oBAAiB;oDAChB,OAAO;oDACP,UAAU,aAAa,KAAK,GAAG;8DAE/B,cAAA,6VAAC,2QAAA,CAAA,UAAI;wDACH,MAAM,KAAK,GAAG;wDACb,GAAI,KAAK,GAAG,CAAC,UAAU,CAAC,WAAW;4DAClC,QAAQ;4DACR,KAAK;wDACP,CAAC;;0EAED,6VAAC,KAAK,IAAI;;;;;0EACV,6VAAC;0EAAM,KAAK,KAAK;;;;;;;;;;;;;;;;;+CAbD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;kCAsB1C,6VAAC,4JAAA,CAAA,gBAAa;;;;;;;;;;;0BAIhB,6VAAC,4JAAA,CAAA,eAAY;gBAAC,WAAU;0BACrB;;;;;;;;AAIT", "debugId": null}}]}