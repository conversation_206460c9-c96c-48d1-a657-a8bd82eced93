#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/terser@5.39.0/node_modules/terser/bin/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/terser@5.39.0/node_modules/terser/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/terser@5.39.0/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/terser@5.39.0/node_modules/terser/bin/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/terser@5.39.0/node_modules/terser/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/terser@5.39.0/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubentweb/cubent/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../terser/bin/terser" "$@"
else
  exec node  "$basedir/../../../terser/bin/terser" "$@"
fi
